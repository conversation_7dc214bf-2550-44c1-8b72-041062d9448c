# 第1批次：盈利能力与估值关系Alpha (1-100) - 重构版
# 基于纯基本面财务数据的经济学理论回归残差分析

# Alpha 1-10: 净利润与总资产关系 (ROA分解分析)
# 经济学逻辑：净利润与总资产的回归残差反映超越平均资产回报率的盈利能力，体现管理效率和竞争优势
residual = ts_regression(ts_zscore(fnd6_ni,500), ts_zscore(fnd6_at,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_nicon,500), ts_zscore(fnd6_at,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_ib,500), ts_zscore(fnd6_at,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_ibcom,500), ts_zscore(fnd6_at,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_pi,500), ts_zscore(fnd6_at,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_oiadp,500), ts_zscore(fnd6_at,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_oibdp,500), ts_zscore(fnd6_at,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(ebit,500), ts_zscore(fnd6_at,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(ebitda,500), ts_zscore(fnd6_at,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_gp,500), ts_zscore(fnd6_at,500),500); residual/ts_std_dev(residual,500)

# Alpha 11-20: 净利润与EBITDA关系 (盈利质量分析)
# 经济学逻辑：净利润与EBITDA的回归残差反映财务费用、税务和折旧政策的影响，体现盈利质量
residual = ts_regression(ts_zscore(fnd6_ni,500), ts_zscore(ebitda,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_nicon,500), ts_zscore(ebitda,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_ib,500), ts_zscore(ebitda,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_ibcom,500), ts_zscore(ebitda,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_pi,500), ts_zscore(ebitda,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_ni,500), ts_zscore(ebit,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_nicon,500), ts_zscore(ebit,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_ib,500), ts_zscore(ebit,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_ibcom,500), ts_zscore(ebit,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_pi,500), ts_zscore(ebit,500),500); residual/ts_std_dev(residual,500)

# Alpha 21-30: EBIT与利息费用关系 (利息覆盖能力分析)
# 经济学逻辑：EBIT与利息费用的回归残差反映超越正常利息覆盖水平的盈利能力，体现财务安全边际
residual = ts_regression(ts_zscore(ebit,500), ts_zscore(fnd6_xint,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(ebitda,500), ts_zscore(fnd6_xint,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_oiadp,500), ts_zscore(fnd6_xint,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_oibdp,500), ts_zscore(fnd6_xint,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_pi,500), ts_zscore(fnd6_xint,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(ebit,500), ts_zscore(fnd6_dltt,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(ebitda,500), ts_zscore(fnd6_dltt,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_oiadp,500), ts_zscore(fnd6_dltt,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_oibdp,500), ts_zscore(fnd6_dltt,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_pi,500), ts_zscore(fnd6_dltt,500),500); residual/ts_std_dev(residual,500)

# Alpha 31-40: 毛利润与收入关系 (规模经济效应分析)
# 经济学逻辑：毛利润与收入的回归残差反映超越线性关系的盈利能力，体现定价权和成本控制能力
residual = ts_regression(ts_zscore(fnd6_gp,500), ts_zscore(fnd6_sale,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_gp,500), ts_zscore(fnd6_revt,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_gp,500), ts_zscore(revenue,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_cogs,500), ts_zscore(fnd6_sale,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_cogs,500), ts_zscore(fnd6_revt,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_cogs,500), ts_zscore(revenue,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_xsga,500), ts_zscore(fnd6_sale,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_xsga,500), ts_zscore(fnd6_revt,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_xsga,500), ts_zscore(revenue,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_xrd,500), ts_zscore(fnd6_sale,500),500); residual/ts_std_dev(residual,500)

# Alpha 41-50: 净利润与股东权益关系 (ROE分解分析)
# 经济学逻辑：净利润与股东权益的回归残差反映超越平均ROE水平的盈利能力，体现管理层价值创造能力
residual = ts_regression(ts_zscore(fnd6_ni,500), ts_zscore(fnd6_ceq,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_nicon,500), ts_zscore(fnd6_ceq,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_ib,500), ts_zscore(fnd6_ceq,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_ibcom,500), ts_zscore(fnd6_ceq,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_pi,500), ts_zscore(fnd6_ceq,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_ni,500), ts_zscore(fnd6_seq,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_nicon,500), ts_zscore(fnd6_seq,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_ib,500), ts_zscore(fnd6_seq,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_ibcom,500), ts_zscore(fnd6_seq,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_pi,500), ts_zscore(fnd6_seq,500),500); residual/ts_std_dev(residual,500)

# Alpha 51-60: 经营现金流与净利润关系 (盈利质量分析)
# 经济学逻辑：经营现金流与净利润的回归残差反映盈利质量，残差为正表明现金流质量优于会计利润
residual = ts_regression(ts_zscore(fnd6_oancf,500), ts_zscore(fnd6_ni,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(cashflow_op,500), ts_zscore(fnd6_ni,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_oancf,500), ts_zscore(fnd6_nicon,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(cashflow_op,500), ts_zscore(fnd6_nicon,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_oancf,500), ts_zscore(fnd6_ib,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(cashflow_op,500), ts_zscore(fnd6_ib,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_oancf,500), ts_zscore(fnd6_ibcom,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(cashflow_op,500), ts_zscore(fnd6_ibcom,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_oancf,500), ts_zscore(fnd6_pi,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(cashflow_op,500), ts_zscore(fnd6_pi,500),500); residual/ts_std_dev(residual,500)

# Alpha 61-70: 自由现金流与资本支出关系 (投资效率分析)
# 经济学逻辑：自由现金流与资本支出的回归残差反映投资后的现金创造能力，体现投资效率
residual = ts_regression(ts_zscore(fnd6_fincf,500), ts_zscore(fnd6_capx,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(cashflow,500), ts_zscore(fnd6_capx,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_oancf,500), ts_zscore(fnd6_capx,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(cashflow_op,500), ts_zscore(fnd6_capx,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_fincf,500), ts_zscore(fnd6_aqc,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(cashflow,500), ts_zscore(fnd6_aqc,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_oancf,500), ts_zscore(fnd6_aqc,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(cashflow_op,500), ts_zscore(fnd6_aqc,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_fincf,500), ts_zscore(fnd6_ppent,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(cashflow,500), ts_zscore(fnd6_ppent,500),500); residual/ts_std_dev(residual,500)

# Alpha 71-80: 债务与现金流关系 (偿债能力分析)
# 经济学逻辑：债务与现金流的回归残差反映超越正常偿债水平的现金创造能力，体现财务安全性
residual = ts_regression(ts_zscore(fnd6_dltt,500), ts_zscore(fnd6_oancf,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_dlc,500), ts_zscore(fnd6_oancf,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(debt,500), ts_zscore(fnd6_oancf,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_dltt,500), ts_zscore(cashflow_op,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_dlc,500), ts_zscore(cashflow_op,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(debt,500), ts_zscore(cashflow_op,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_dltt,500), ts_zscore(ebitda,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_dlc,500), ts_zscore(ebitda,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(debt,500), ts_zscore(ebitda,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_dltt,500), ts_zscore(ebit,500),500); residual/ts_std_dev(residual,500)

# Alpha 81-90: 营运资本与收入关系 (营运效率分析)
# 经济学逻辑：营运资本与收入的回归残差反映营运资本管理效率，体现现金转换周期优化能力
residual = ts_regression(ts_zscore(fnd6_wcap,500), ts_zscore(fnd6_sale,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_wcap,500), ts_zscore(fnd6_revt,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_wcap,500), ts_zscore(revenue,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_rect,500), ts_zscore(fnd6_sale,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_rect,500), ts_zscore(fnd6_revt,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_rect,500), ts_zscore(revenue,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_invt,500), ts_zscore(fnd6_sale,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_invt,500), ts_zscore(fnd6_revt,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_invt,500), ts_zscore(revenue,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_ap,500), ts_zscore(fnd6_cogs,500),500); residual/ts_std_dev(residual,500)

# Alpha 91-100: 税务效率与盈利能力关系 (税务管理分析)
# 经济学逻辑：税务费用与税前利润的回归残差反映税务管理效率，体现有效税率优化能力
residual = ts_regression(ts_zscore(fnd6_txt,500), ts_zscore(fnd6_pi,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_txt,500), ts_zscore(fnd6_ib,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_txt,500), ts_zscore(fnd6_ibcom,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_txt,500), ts_zscore(ebit,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_txt,500), ts_zscore(ebitda,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_txdi,500), ts_zscore(fnd6_pi,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_txdi,500), ts_zscore(fnd6_ib,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_txdi,500), ts_zscore(fnd6_ibcom,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_txdi,500), ts_zscore(ebit,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(fnd6_txdi,500), ts_zscore(ebitda,500),500); residual/ts_std_dev(residual,500)

# Alpha 61-70: 自由现金流与经营现金流关系
# 经济学逻辑：自由现金流扣除资本支出，反映企业真正可分配的现金，与经营现金流差异体现投资强度
residual = ts_regression(ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_mean,500), ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_median,500), ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_high,500), ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_high,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_low,500), ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_low,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_std,500), ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_std,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_mean,500), ts_zscore(pv87_ann_matrix_depreciation_amortization_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_mean,500), ts_zscore(pv87_ann_matrix_depreciation_amortization_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_median,500), ts_zscore(pv87_ann_matrix_depreciation_amortization_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_median,500), ts_zscore(pv87_ann_matrix_depreciation_amortization_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_std,500), ts_zscore(pv87_ann_matrix_depreciation_amortization_estimate_std,500),500); residual/ts_std_dev(residual,500)

# Alpha 71-80: 净债务与EBITDA关系
# 经济学逻辑：净债务与EBITDA比率是重要的杠杆指标，反映企业偿债能力和财务风险
residual = ts_regression(ts_zscore(pv87_ann_matrix_net_debt_estimate_mean,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_net_debt_estimate_median,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_net_debt_estimate_high,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_high,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_net_debt_estimate_low,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_low,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_ndebt_fp1,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_median_ndebt_fp1,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_high_ndebt_fp1,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_high,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_low_ndebt_fp1,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_low,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_ndebt_fp2,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_ndebt_fp3,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_mean,500),500); residual/ts_std_dev(residual,500)

# Alpha 81-90: 有效税率与税前利润关系
# 经济学逻辑：有效税率反映税务效率，与税前利润的关系体现税务管理能力
residual = ts_regression(ts_zscore(pv87_ann_matrix_effective_tax_rate_estimate_mean,500), ts_zscore(pv87_ann_matrix_ebt_gaap_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_effective_tax_rate_estimate_median,500), ts_zscore(pv87_ann_matrix_ebt_gaap_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_effective_tax_rate_estimate_high,500), ts_zscore(pv87_ann_matrix_ebt_gaap_estimate_high,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_effective_tax_rate_estimate_low,500), ts_zscore(pv87_ann_matrix_ebt_gaap_estimate_low,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_effective_tax_rate_estimate_std,500), ts_zscore(pv87_ann_matrix_ebt_gaap_estimate_std,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_effective_tax_rate_estimate_mean,500), ts_zscore(pv87_ann_matrix_ebt_normalized_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_effective_tax_rate_estimate_median,500), ts_zscore(pv87_ann_matrix_ebt_normalized_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_effective_tax_rate_estimate_high,500), ts_zscore(pv87_ann_matrix_ebt_normalized_estimate_high,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_effective_tax_rate_estimate_low,500), ts_zscore(pv87_ann_matrix_ebt_normalized_estimate_low,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_effective_tax_rate_estimate_std,500), ts_zscore(pv87_ann_matrix_ebt_normalized_estimate_std,500),500); residual/ts_std_dev(residual,500)

# Alpha 91-100: 分析师预期一致性与盈利指标关系
# 经济学逻辑：分析师预期的标准差反映不确定性，与盈利指标的关系体现业绩可预测性
residual = ts_regression(ts_zscore(pv87_ann_matrix_eps_gaap_estimate_std,500), ts_zscore(pv87_ann_matrix_eps_gaap_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_revenue_estimate_std,500), ts_zscore(pv87_ann_matrix_revenue_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_ebitda_estimate_std,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_ebit_estimate_std,500), ts_zscore(pv87_ann_matrix_ebit_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_net_income_gaap_estimate_std,500), ts_zscore(pv87_ann_matrix_net_income_gaap_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_stddev_eps_fp1,500), ts_zscore(anl14_mean_eps_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_stddev_revenue_fp1,500), ts_zscore(anl14_mean_revenue_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_stddev_roe_fp1,500), ts_zscore(anl14_mean_roe_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_stddev_roa_fp1,500), ts_zscore(anl14_mean_roa_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_eps_normalized_estimate_std,500), ts_zscore(pv87_ann_matrix_eps_normalized_estimate_mean,500),500); residual/ts_std_dev(residual,500)
